-- Crow Framework - Client Main

CrowFramework = {}
CrowFramework.PlayerData = {}
CrowFramework.PlayerLoaded = false

-- Framework initialisering
Citizen.CreateThread(function()
    while not NetworkIsSessionStarted() do
        Citizen.Wait(100)
    end
    
    CrowUtils.Debug('Client framework initializing...')
    
    -- Me<PERSON>ela servern att klienten är redo
    TriggerServerEvent(CrowEvents.Player.READY)
    
    CrowUtils.Debug('Client framework initialized')
end)

-- Hämta framework objekt
function CrowFramework.GetPlayerData()
    return CrowFramework.PlayerData
end

function CrowFramework.IsPlayerLoaded()
    return CrowFramework.PlayerLoaded
end

-- Event handlers
CrowEvents.RegisterNetEvent(CrowEvents.Player.LOADED, function(playerData)
    CrowFramework.PlayerData = playerData
    CrowFramework.PlayerLoaded = true

    CrowUtils.Debug('Player data loaded: %s %s', playerData.firstname, playerData.lastname)

    -- Spawna spelaren
    CrowFramework.SpawnPlayer()

    -- Visa HUD
    CrowFramework.ShowHUD()
    CrowFramework.UpdateHUD()
end)

CrowEvents.RegisterNetEvent(CrowEvents.Player.UPDATED, function(playerData)
    CrowFramework.PlayerData = playerData
    CrowUtils.Debug('Player data updated')
end)

CrowEvents.RegisterNetEvent(CrowEvents.Player.MONEY_UPDATED, function(money, bank)
    if CrowFramework.PlayerData then
        CrowFramework.PlayerData.money = money
        CrowFramework.PlayerData.bank = bank
        CrowUtils.Debug('Money updated: Cash $%d, Bank $%d', money, bank)

        -- Uppdatera HUD
        CrowFramework.UpdateHUD()
    end
end)

CrowEvents.RegisterNetEvent(CrowEvents.Player.JOB_UPDATED, function(jobData)
    if CrowFramework.PlayerData then
        CrowFramework.PlayerData.job = jobData.name
        CrowFramework.PlayerData.job_grade = jobData.grade
        CrowUtils.Debug('Job updated: %s (Grade %d)', jobData.label, jobData.grade)

        -- Uppdatera HUD
        CrowFramework.UpdateHUD()
    end
end)

-- Spawning
function CrowFramework.SpawnPlayer()
    if not CrowFramework.PlayerLoaded then
        return
    end
    
    local playerData = CrowFramework.PlayerData
    local spawnPoint = Config.SpawnPoints[1] -- Default spawn
    
    -- Använd sparad position om den finns
    if playerData.position and playerData.position.x then
        spawnPoint = {
            coords = vector4(playerData.position.x, playerData.position.y, playerData.position.z, playerData.position.h or 0.0)
        }
    end
    
    -- Skapa ped om den inte finns
    if not DoesEntityExist(PlayerPedId()) then
        local model = GetHashKey('mp_male')
        RequestModel(model)
        
        while not HasModelLoaded(model) do
            Citizen.Wait(100)
        end
        
        local ped = CreatePed(4, model, spawnPoint.coords.x, spawnPoint.coords.y, spawnPoint.coords.z, spawnPoint.coords.w, true, false)
        SetPlayerModel(PlayerId(), model)
        SetModelAsNoLongerNeeded(model)
    end
    
    -- Teleportera till spawn punkt
    local ped = PlayerPedId()
    SetEntityCoords(ped, spawnPoint.coords.x, spawnPoint.coords.y, spawnPoint.coords.z, false, false, false, true)
    SetEntityHeading(ped, spawnPoint.coords.w)
    
    -- Sätt hälsa
    SetEntityHealth(ped, 200)
    
    -- Frys spelaren kort för att undvika problem
    FreezeEntityPosition(ped, true)
    
    Citizen.SetTimeout(2000, function()
        FreezeEntityPosition(ped, false)
        CrowUtils.Debug('Player spawned successfully')
        
        -- Meddela servern att spelaren har spawnat
        TriggerServerEvent(CrowEvents.Player.SPAWNED)
    end)
end

-- Spara spelarposition regelbundet
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(30000) -- Spara var 30:e sekund
        
        if CrowFramework.PlayerLoaded then
            local ped = PlayerPedId()
            if DoesEntityExist(ped) then
                local coords = GetEntityCoords(ped)
                local heading = GetEntityHeading(ped)
                
                CrowFramework.PlayerData.position = {
                    x = coords.x,
                    y = coords.y,
                    z = coords.z,
                    h = heading
                }
                
                -- Skicka till server för sparning (kan optimeras)
                TriggerServerEvent('crow:player:updatePosition', CrowFramework.PlayerData.position)
            end
        end
    end
end)

-- Död hantering
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)
        
        if CrowFramework.PlayerLoaded then
            local ped = PlayerPedId()
            
            if IsEntityDead(ped) then
                TriggerServerEvent(CrowEvents.Player.DIED)
                
                -- Vänta tills spelaren respawnar
                while IsEntityDead(ped) do
                    Citizen.Wait(1000)
                    ped = PlayerPedId()
                end
                
                -- Respawn spelaren
                CrowFramework.SpawnPlayer()
            end
        end
    end
end)

-- Exportera framework
exports('GetFrameworkObject', function()
    return CrowFramework
end)

exports('GetPlayerData', function()
    return CrowFramework.GetPlayerData()
end)

exports('IsPlayerLoaded', function()
    return CrowFramework.IsPlayerLoaded()
end)

-- Hjälpfunktioner
function CrowFramework.ShowNotification(data)
    TriggerEvent(CrowEvents.UI.SHOW_NOTIFICATION, data)
end

function CrowFramework.GetPlayerMoney()
    if CrowFramework.PlayerData then
        return CrowFramework.PlayerData.money or 0, CrowFramework.PlayerData.bank or 0
    end
    return 0, 0
end

function CrowFramework.GetPlayerJob()
    if CrowFramework.PlayerData then
        return {
            name = CrowFramework.PlayerData.job or 'unemployed',
            grade = CrowFramework.PlayerData.job_grade or 0
        }
    end
    return {name = 'unemployed', grade = 0}
end

-- Exportera hjälpfunktioner
exports('ShowNotification', function(data)
    CrowFramework.ShowNotification(data)
end)

exports('GetPlayerMoney', function()
    return CrowFramework.GetPlayerMoney()
end)

exports('GetPlayerJob', function()
    return CrowFramework.GetPlayerJob()
end)

-- HUD funktioner
function CrowFramework.ShowHUD()
    SendNUIMessage({
        action = 'showHUD'
    })
end

function CrowFramework.HideHUD()
    SendNUIMessage({
        action = 'hideHUD'
    })
end

function CrowFramework.UpdateHUD()
    if not CrowFramework.PlayerLoaded or not CrowFramework.PlayerData then
        return
    end

    local ped = PlayerPedId()
    local hudData = {
        money = CrowFramework.PlayerData.money or 0,
        bank = CrowFramework.PlayerData.bank or 0,
        job = {
            name = CrowFramework.PlayerData.job or 'unemployed',
            label = Config.Jobs[CrowFramework.PlayerData.job] and Config.Jobs[CrowFramework.PlayerData.job].label or 'Arbetslös'
        }
    }

    -- Lägg till hälso- och staminadata
    if DoesEntityExist(ped) then
        -- Spelare hälsa (0-200, konvertera till procent)
        local health = GetEntityHealth(ped)
        local maxHealth = GetEntityMaxHealth(ped)
        hudData.playerHealth = (health / maxHealth) * 100

        -- Spelare stamina (säker approach för RedM)
        if GetPedStaminaNormalized then
            hudData.playerStamina = GetPedStaminaNormalized(ped) * 100
        else
            hudData.playerStamina = 100 -- Default värde om funktionen inte finns
        end

        -- Kontrollera om spelaren är på häst (säker approach för RedM)
        local mount = 0
        if GetMount then
            mount = GetMount(ped)
        end

        if mount and mount ~= 0 and DoesEntityExist(mount) then
            hudData.onHorse = true

            -- Häst hälsa
            local horseHealth = GetEntityHealth(mount)
            local horseMaxHealth = GetEntityMaxHealth(mount)
            if horseMaxHealth > 0 then
                hudData.horseHealth = (horseHealth / horseMaxHealth) * 100
            else
                hudData.horseHealth = 100
            end

            -- Häst stamina (säker approach)
            if GetPedStaminaNormalized then
                hudData.horseStamina = GetPedStaminaNormalized(mount) * 100
            else
                hudData.horseStamina = 100
            end
        else
            hudData.onHorse = false
        end
    end

    SendNUIMessage({
        action = 'updateHUD',
        hudData = hudData
    })
end

-- Exportera HUD funktioner
exports('ShowHUD', function()
    CrowFramework.ShowHUD()
end)

exports('HideHUD', function()
    CrowFramework.HideHUD()
end)

exports('UpdateHUD', function()
    CrowFramework.UpdateHUD()
end)

-- Uppdatera HUD regelbundet
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(500) -- Uppdatera var 500ms

        if CrowFramework.PlayerLoaded then
            CrowFramework.UpdateHUD()
        end
    end
end)
