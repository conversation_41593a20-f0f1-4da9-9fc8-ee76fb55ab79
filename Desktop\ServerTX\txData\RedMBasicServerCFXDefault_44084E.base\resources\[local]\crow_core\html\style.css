/* Crow Framework - Main Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    overflow: hidden;
    user-select: none;
}

/* Grundläggande färgschema */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --info-color: #3498db;
    --text-color: #ecf0f1;
    --text-dark: #2c3e50;
    --background-dark: rgba(44, 62, 80, 0.9);
    --background-light: rgba(255, 255, 255, 0.9);
    --border-radius: 8px;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Allmänna klasser */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.3s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-in-out;
}

.slide-out-right {
    animation: slideOutRight 0.3s ease-in-out;
}

/* Animationer */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideInRight {
    from { 
        transform: translateX(100%);
        opacity: 0;
    }
    to { 
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from { 
        transform: translateX(0);
        opacity: 1;
    }
    to { 
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes slideInLeft {
    from { 
        transform: translateX(-100%);
        opacity: 0;
    }
    to { 
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutLeft {
    from { 
        transform: translateX(0);
        opacity: 1;
    }
    to { 
        transform: translateX(-100%);
        opacity: 0;
    }
}

@keyframes slideInDown {
    from { 
        transform: translateY(-100%);
        opacity: 0;
    }
    to { 
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideOutUp {
    from { 
        transform: translateY(0);
        opacity: 1;
    }
    to { 
        transform: translateY(-100%);
        opacity: 0;
    }
}

@keyframes slideInUp {
    from { 
        transform: translateY(100%);
        opacity: 0;
    }
    to { 
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideOutDown {
    from { 
        transform: translateY(0);
        opacity: 1;
    }
    to { 
        transform: translateY(100%);
        opacity: 0;
    }
}

/* Knappar */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--text-color);
}

.btn-success:hover {
    background-color: #229954;
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--text-color);
}

.btn-warning:hover {
    background-color: #e67e22;
}

.btn-error {
    background-color: var(--error-color);
    color: var(--text-color);
}

.btn-error:hover {
    background-color: #c0392b;
}

/* Input fält */
.input {
    padding: 10px;
    border: 2px solid var(--secondary-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    background-color: var(--background-light);
    color: var(--text-dark);
    transition: var(--transition);
}

.input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Custom HUD */
.custom-hud {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    pointer-events: none;
}

.hud-money {
    background: var(--background-dark);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 10px;
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--success-color);
    min-width: 200px;
}

.money-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: var(--text-color);
}

.money-item:last-child {
    margin-bottom: 0;
}

.money-icon {
    font-size: 16px;
    margin-right: 8px;
    width: 20px;
}

.money-label {
    font-size: 14px;
    margin-right: 10px;
    flex: 1;
}

.money-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--success-color);
}

/* Spelare och häst stats */
.hud-player-stats, .hud-horse-stats {
    background: var(--background-dark);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 10px;
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--error-color);
    min-width: 200px;
}

.hud-horse-stats {
    border-left-color: var(--warning-color);
}

.horse-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: var(--text-color);
}

.horse-icon {
    font-size: 16px;
    margin-right: 8px;
}

.horse-label {
    font-size: 14px;
    font-weight: 600;
}

.stat-bar {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.stat-bar:last-child {
    margin-bottom: 0;
}

.stat-icon {
    font-size: 14px;
    margin-right: 8px;
    width: 20px;
}

.stat-bar-container {
    flex: 1;
    height: 8px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    margin-right: 10px;
    overflow: hidden;
}

.stat-bar-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.health-bar {
    background: linear-gradient(90deg, var(--error-color), #e74c3c);
}

.stamina-bar {
    background: linear-gradient(90deg, var(--warning-color), #f39c12);
}

.stat-text {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-color);
    min-width: 35px;
    text-align: right;
}

.hud-job {
    background: var(--background-dark);
    border-radius: var(--border-radius);
    padding: 12px 15px;
    margin-bottom: 10px;
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--info-color);
    display: flex;
    align-items: center;
    color: var(--text-color);
}

.job-icon {
    font-size: 16px;
    margin-right: 10px;
}

.job-name {
    font-size: 14px;
    font-weight: 500;
}

.hud-time {
    background: var(--background-dark);
    border-radius: var(--border-radius);
    padding: 12px 15px;
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--warning-color);
    text-align: center;
    color: var(--text-color);
}

.time-display {
    font-size: 16px;
    font-weight: 600;
    color: var(--warning-color);
}

/* HUD Animations */
.custom-hud.show {
    animation: hudSlideIn 0.5s ease-out;
}

.custom-hud.hide {
    animation: hudSlideOut 0.5s ease-in;
}

@keyframes hudSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes hudSlideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Modal/Dialog */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--background-light);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    max-width: 500px;
    width: 90%;
    max-height: 80%;
    overflow-y: auto;
}

.modal-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--secondary-color);
}

.modal-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.modal-body {
    margin-bottom: 20px;
    color: var(--text-dark);
    line-height: 1.6;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 15px;
    border-top: 2px solid var(--secondary-color);
}
