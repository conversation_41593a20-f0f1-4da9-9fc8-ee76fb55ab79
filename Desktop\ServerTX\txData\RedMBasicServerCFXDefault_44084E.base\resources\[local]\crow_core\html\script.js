// Crow Framework - Main JavaScript

// Global framework object
window.CrowFramework = {
    ready: false,
    playerData: null,
    
    // Initialize framework
    init() {
        this.ready = true;
        console.log('Crow Framework UI initialized');
    },
    
    // Update player data
    updatePlayerData(data) {
        this.playerData = data;
        this.triggerEvent('playerDataUpdated', data);
    },
    
    // Event system
    events: {},
    
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    },
    
    off(event, callback) {
        if (!this.events[event]) return;
        const index = this.events[event].indexOf(callback);
        if (index > -1) {
            this.events[event].splice(index, 1);
        }
    },
    
    triggerEvent(event, ...args) {
        if (!this.events[event]) return;
        this.events[event].forEach(callback => {
            try {
                callback(...args);
            } catch (error) {
                console.error(`Error in event handler for ${event}:`, error);
            }
        });
    }
};

// Message handler for communication with Lua
window.addEventListener('message', (event) => {
    const data = event.data;
    
    switch (data.action) {
        case 'init':
            CrowFramework.init();
            break;
            
        case 'updatePlayerData':
            CrowFramework.updatePlayerData(data.playerData);
            break;
            
        case 'showUI':
            showUI(data.ui, data.data);
            break;
            
        case 'hideUI':
            hideUI(data.ui);
            break;

        case 'updateHUD':
            updateHUD(data.hudData);
            break;

        case 'showHUD':
            showHUD();
            break;

        case 'hideHUD':
            hideHUD();
            break;

        default:
            // Pass unknown actions to custom handlers
            CrowFramework.triggerEvent('message', data);
            break;
    }
});

// UI Management
const activeUIs = new Set();

function showUI(uiName, data = {}) {
    const uiElement = document.getElementById(uiName);
    if (!uiElement) {
        console.warn(`UI element '${uiName}' not found`);
        return;
    }
    
    uiElement.classList.remove('hidden');
    uiElement.classList.add('fade-in');
    activeUIs.add(uiName);
    
    // Trigger show event
    CrowFramework.triggerEvent(`ui:${uiName}:show`, data);
    
    // Enable cursor if needed
    if (data.enableCursor !== false) {
        enableCursor();
    }
}

function hideUI(uiName) {
    const uiElement = document.getElementById(uiName);
    if (!uiElement) {
        console.warn(`UI element '${uiName}' not found`);
        return;
    }
    
    uiElement.classList.add('fade-out');
    
    setTimeout(() => {
        uiElement.classList.add('hidden');
        uiElement.classList.remove('fade-in', 'fade-out');
        activeUIs.delete(uiName);
        
        // Trigger hide event
        CrowFramework.triggerEvent(`ui:${uiName}:hide`);
        
        // Disable cursor if no UIs are active
        if (activeUIs.size === 0) {
            disableCursor();
        }
    }, 300);
}

function hideAllUIs() {
    activeUIs.forEach(uiName => {
        hideUI(uiName);
    });
}

// Cursor management
let cursorEnabled = false;

function enableCursor() {
    if (!cursorEnabled) {
        cursorEnabled = true;
        document.body.style.pointerEvents = 'auto';
        
        // Send message to Lua to enable cursor
        fetch(`https://${GetParentResourceName()}/enableCursor`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ enable: true })
        });
    }
}

function disableCursor() {
    if (cursorEnabled) {
        cursorEnabled = false;
        document.body.style.pointerEvents = 'none';
        
        // Send message to Lua to disable cursor
        fetch(`https://${GetParentResourceName()}/disableCursor`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ enable: false })
        });
    }
}

// Utility functions
function sendMessage(action, data = {}) {
    fetch(`https://${GetParentResourceName()}/${action}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    }).catch(error => {
        console.error('Error sending message:', error);
    });
}

function formatMoney(amount) {
    return new Intl.NumberFormat('sv-SE', {
        style: 'currency',
        currency: 'SEK',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount).replace('SEK', '$');
}

function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
}

// Keyboard event handling
document.addEventListener('keydown', (event) => {
    // ESC key to close UIs
    if (event.key === 'Escape') {
        if (activeUIs.size > 0) {
            event.preventDefault();
            hideAllUIs();
        }
    }
    
    // Trigger custom keyboard events
    CrowFramework.triggerEvent('keydown', event);
});

document.addEventListener('keyup', (event) => {
    CrowFramework.triggerEvent('keyup', event);
});

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('Crow Framework UI DOM loaded');
    
    // Send ready message to Lua
    sendMessage('uiReady');
});

// HUD Management
let hudVisible = false;

function updateHUD(hudData) {
    if (!hudData) return;

    // Uppdatera pengar
    if (hudData.money !== undefined) {
        const cashElement = document.getElementById('money-cash');
        if (cashElement) {
            cashElement.textContent = formatMoney(hudData.money);
        }
    }

    if (hudData.bank !== undefined) {
        const bankElement = document.getElementById('money-bank');
        if (bankElement) {
            bankElement.textContent = formatMoney(hudData.bank);
        }
    }

    // Uppdatera spelare hälsa
    if (hudData.playerHealth !== undefined) {
        const healthBar = document.getElementById('player-health-bar');
        const healthText = document.getElementById('player-health-text');
        if (healthBar && healthText) {
            const healthPercent = Math.round(hudData.playerHealth);
            healthBar.style.width = healthPercent + '%';
            healthText.textContent = healthPercent + '%';
        }
    }

    // Uppdatera spelare stamina
    if (hudData.playerStamina !== undefined) {
        const staminaBar = document.getElementById('player-stamina-bar');
        const staminaText = document.getElementById('player-stamina-text');
        if (staminaBar && staminaText) {
            const staminaPercent = Math.round(hudData.playerStamina);
            staminaBar.style.width = staminaPercent + '%';
            staminaText.textContent = staminaPercent + '%';
        }
    }

    // Uppdatera häst stats (visa/dölj baserat på om spelaren är på häst)
    const horseStats = document.getElementById('horse-stats');
    if (hudData.onHorse !== undefined) {
        if (hudData.onHorse) {
            horseStats.classList.remove('hidden');

            // Uppdatera häst hälsa
            if (hudData.horseHealth !== undefined) {
                const horseHealthBar = document.getElementById('horse-health-bar');
                const horseHealthText = document.getElementById('horse-health-text');
                if (horseHealthBar && horseHealthText) {
                    const horseHealthPercent = Math.round(hudData.horseHealth);
                    horseHealthBar.style.width = horseHealthPercent + '%';
                    horseHealthText.textContent = horseHealthPercent + '%';
                }
            }

            // Uppdatera häst stamina
            if (hudData.horseStamina !== undefined) {
                const horseStaminaBar = document.getElementById('horse-stamina-bar');
                const horseStaminaText = document.getElementById('horse-stamina-text');
                if (horseStaminaBar && horseStaminaText) {
                    const horseStaminaPercent = Math.round(hudData.horseStamina);
                    horseStaminaBar.style.width = horseStaminaPercent + '%';
                    horseStaminaText.textContent = horseStaminaPercent + '%';
                }
            }
        } else {
            horseStats.classList.add('hidden');
        }
    }

    // Uppdatera jobb
    if (hudData.job) {
        const jobElement = document.getElementById('job-name');
        if (jobElement) {
            jobElement.textContent = hudData.job.label || hudData.job.name || 'Arbetslös';
        }
    }

    // Uppdatera tid
    if (hudData.time) {
        const timeElement = document.getElementById('game-time');
        if (timeElement) {
            timeElement.textContent = hudData.time;
        }
    }
}

function showHUD() {
    const hudElement = document.getElementById('custom-hud');
    if (hudElement) {
        hudElement.classList.remove('hidden', 'hide');
        hudElement.classList.add('show');
        hudVisible = true;
    }
}

function hideHUD() {
    const hudElement = document.getElementById('custom-hud');
    if (hudElement) {
        hudElement.classList.remove('show');
        hudElement.classList.add('hide');

        setTimeout(() => {
            hudElement.classList.add('hidden');
            hudElement.classList.remove('hide');
        }, 500);

        hudVisible = false;
    }
}

function toggleHUD() {
    if (hudVisible) {
        hideHUD();
    } else {
        showHUD();
    }
}

// Uppdatera tid regelbundet
setInterval(() => {
    const now = new Date();
    const timeString = now.toLocaleTimeString('sv-SE', {
        hour: '2-digit',
        minute: '2-digit'
    });

    const timeElement = document.getElementById('game-time');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}, 1000);

// Export for global access
window.showUI = showUI;
window.hideUI = hideUI;
window.hideAllUIs = hideAllUIs;
window.sendMessage = sendMessage;
window.formatMoney = formatMoney;
window.formatTime = formatTime;
window.updateHUD = updateHUD;
window.showHUD = showHUD;
window.hideHUD = hideHUD;
window.toggleHUD = toggleHUD;
