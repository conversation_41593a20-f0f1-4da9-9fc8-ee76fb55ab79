-- Crow Framework - HUD Management (RedM Version)

local hudHidden = false

-- Enkel HUD-hantering för RedM
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)

        -- Förhindra ALT-menyn (Player List) - RedM specifika kontroller
        DisableControlAction(0, 0x26E9DC00, true) -- INPUT_MULTIPLAYER_INFO (ALT)
        DisableControlAction(0, 0x3C3DD371, true) -- INPUT_SELECTWHEEL_NAV (ALT)

        -- Dölj minimap/radar om det är aktivt
        if IsRadarEnabled() then
            DisplayRadar(false)
        end
    end
end)

-- Enklare approach för RedM - bara dölj radar och förhindra ALT
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(100) -- <PERSON><PERSON> frequent för bättre prestanda

        -- Förhindra att radarn blir stor
        if IsRadarEnabled() then
            DisplayRadar(false)
        end

        -- Dölj minimap helt
        SetRadarZoom(0)
    end
end)

-- Hantera när spelare trycker ALT
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        -- Kontrollera om ALT trycks
        if IsControlPressed(0, 0x26E9DC00) then -- ALT key
            -- Förhindra standard ALT-funktionalitet
            DisableControlAction(0, 0x26E9DC00, true)
            
            -- Här kan du lägga till egen funktionalitet istället
            -- T.ex. visa egen spellista eller meny
        end
    end
end)

-- Rensa meddelanden regelbundet
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(5000) -- Var 5:e sekund

        -- Rensa alla standard notifications
        ClearAllHelpMessages()
    end
end)

-- Funktioner för att kontrollera HUD
function ToggleHUD()
    hudHidden = not hudHidden
    DisplayHud(not hudHidden)
end

function ShowHUD()
    hudHidden = false
    DisplayHud(true)
end

function HideHUD()
    hudHidden = true
    DisplayHud(false)
end

function IsHUDHidden()
    return hudHidden
end

-- Exportera funktioner
exports('ToggleHUD', function()
    ToggleHUD()
end)

exports('ShowHUD', function()
    ShowHUD()
end)

exports('HideHUD', function()
    HideHUD()
end)

exports('IsHUDHidden', function()
    return IsHUDHidden()
end)

-- Kommando för att toggle HUD (för testing)
RegisterCommand('togglehud', function()
    ToggleHUD()
    local status = hudHidden and "dolt" or "visat"
    CrowFramework.ShowNotification({
        type = 'info',
        title = 'HUD',
        message = 'HUD är nu ' .. status
    })
end, false)

-- Enkel loading screen hantering
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)

        -- Förhindra standard loading screen om möjligt
        if GetIsLoadingScreenActive and GetIsLoadingScreenActive() then
            if ShutdownLoadingScreen then
                ShutdownLoadingScreen()
            end
        end
    end
end)
