-- Crow Framework - Client Player Management

-- Spara position regelbundet
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(30000) -- Var 30:e sekund
        
        if CrowFramework.IsPlayerLoaded() then
            local ped = PlayerPedId()
            if DoesEntityExist(ped) then
                local coords = GetEntityCoords(ped)
                local heading = GetEntityHeading(ped)
                
                TriggerServerEvent('crow:player:updatePosition', {
                    x = coords.x,
                    y = coords.y,
                    z = coords.z,
                    h = heading
                })
            end
        end
    end
end)

-- Hantera spelarens hälsa och status
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)
        
        if CrowFramework.IsPlayerLoaded() then
            local ped = PlayerPedId()
            
            if DoesEntityExist(ped) then
                -- Kontrollera hälsa
                local health = GetEntityHealth(ped)
                local maxHealth = GetEntityMaxHealth(ped)
                
                -- Kontrollera om spelaren är skadad
                if health < maxHealth * 0.5 then
                    -- Spelaren är allvarligt skadad
                    -- <PERSON><PERSON>r kan du lägga till effekter, begränsningar etc.
                end
                
                -- Kontrollera om spelaren är död
                if IsEntityDead(ped) then
                    -- Hantera död
                    TriggerServerEvent(CrowEvents.Player.DIED)
                end
            end
        end
    end
end)

-- HUD och UI uppdateringar
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)
        
        if CrowFramework.IsPlayerLoaded() then
            local playerData = CrowFramework.GetPlayerData()
            
            -- Uppdatera HUD med spelarinfo
            -- Detta kan utökas med en riktig HUD
            
            -- Exempel: Visa pengar i chat (kan tas bort senare)
            -- TriggerEvent('chat:addMessage', {
            --     color = { 255, 255, 255 },
            --     multiline = false,
            --     args = { "Pengar", string.format("Kontanter: $%d | Bank: $%d", playerData.money or 0, playerData.bank or 0) }
            -- })
        end
    end
end)

-- Hantera fordon
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(100)
        
        if CrowFramework.IsPlayerLoaded() then
            local ped = PlayerPedId()
            
            if IsPedInAnyVehicle(ped, false) then
                local vehicle = GetVehiclePedIsIn(ped, false)
                
                -- Här kan du lägga till fordonspecifik logik
                -- T.ex. bränslekonsumtion, hastighetsbegränsningar etc.
            end
        end
    end
end)

-- Hantera vapen och strid (RedM version)
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(500) -- Mindre frequent för bättre prestanda

        if CrowFramework.IsPlayerLoaded() then
            local ped = PlayerPedId()

            -- Kontrollera om spelaren har ett vapen (RedM natives)
            local hasWeapon, weaponHash = Citizen.InvokeNative(0x3A87E44BB9A01D54, ped, true) -- GET_CURRENT_PED_WEAPON
            if hasWeapon and weaponHash ~= GetHashKey("WEAPON_UNARMED") then
                -- Här kan du lägga till vapenlogik
                -- T.ex. ammunition, vapen-stats etc.
            end

            -- Kontrollera om spelaren skjuter
            if IsPedShooting(ped) then
                -- Hantera skjutning
                -- T.ex. ljud, effekter, ammunition etc.
            end
        end
    end
end)

-- Interaktioner med världen
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        if CrowFramework.IsPlayerLoaded() then
            local ped = PlayerPedId()
            local coords = GetEntityCoords(ped)
            
            -- Här kan du lägga till interaktioner
            -- T.ex. dörrar, NPCs, objekt etc.
            
            -- Exempel: Visa hjälptext för interaktioner
            -- if IsControlJustPressed(0, 0xE8342FF2) then -- E key
            --     -- Hantera interaktion
            -- end
        end
    end
end)

-- Exportera funktioner
exports('GetPlayerCoords', function()
    if CrowFramework.IsPlayerLoaded() then
        local ped = PlayerPedId()
        if DoesEntityExist(ped) then
            return GetEntityCoords(ped)
        end
    end
    return vector3(0, 0, 0)
end)

exports('GetPlayerHeading', function()
    if CrowFramework.IsPlayerLoaded() then
        local ped = PlayerPedId()
        if DoesEntityExist(ped) then
            return GetEntityHeading(ped)
        end
    end
    return 0.0
end)

exports('IsPlayerInVehicle', function()
    if CrowFramework.IsPlayerLoaded() then
        local ped = PlayerPedId()
        return IsPedInAnyVehicle(ped, false)
    end
    return false
end)

exports('GetPlayerVehicle', function()
    if CrowFramework.IsPlayerLoaded() then
        local ped = PlayerPedId()
        if IsPedInAnyVehicle(ped, false) then
            return GetVehiclePedIsIn(ped, false)
        end
    end
    return 0
end)
